# 服务器配置
SIGNALING_SERVER_PORT=3000
API_SERVER_PORT=8000

# Redis配置
REDIS_URL=redis://localhost:6379

# TURN服务器配置
TURN_SERVER_URL=turn:localhost:3478
TURN_USERNAME=user
TURN_PASSWORD=password

# 数字人模型配置
SADTALKER_MODEL_PATH=./models/sadtalker
WAV2LIP_MODEL_PATH=./models/wav2lip
REAL_ESRGAN_MODEL_PATH=./models/real-esrgan

# 语音处理配置
WHISPER_MODEL_SIZE=base
TTS_MODEL_PATH=./models/coqui-tts
RVC_MODEL_PATH=./models/rvc

# 云端部署配置
HEROKU_APP_NAME=your-app-name
RAILWAY_PROJECT_ID=your-project-id

# 安全配置
JWT_SECRET=your-jwt-secret-key
CORS_ORIGINS=http://localhost:3000,http://localhost:8081

# 文件上传配置
MAX_FILE_SIZE=50MB
UPLOAD_PATH=./uploads

# 日志配置
LOG_LEVEL=info
LOG_FILE=./logs/app.log
