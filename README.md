# 数字人视频通话App

零预算的数字人视频通话应用，基于开源技术栈构建。

## 技术栈

### 数字人生成
- **SadTalker** - 单张图片生成说话视频
- **Wav2Lip** - 唇形同步技术  
- **Real-ESRGAN** - 视频超分辨率增强

### 实时通信
- **WebRTC** - 免费P2P视频通话
- **Socket.io** - 实时信令服务器
- **Coturn** - STUN/TURN服务器

### 语音处理
- **OpenAI Whisper** - 语音转文字
- **Coqui TTS** - 文字转语音
- **RVC** - 实时变声

### 后端框架
- **FastAPI (Python)** - API服务
- **Node.js + Express** - 信令服务器

### 前端
- **React Native** - 跨平台移动端
- **WebRTC-adapter** - 浏览器兼容

## 项目架构

```
客户端 → WebRTC连接 → 信令服务器
数字人引擎 → 实时处理音视频流
云端部署 → 使用免费tier (Heroku/Railway)
```

## 目录结构

```
数字人aa/
├── signaling-server/     # Node.js信令服务器
├── digital-human-api/    # FastAPI数字人引擎
├── mobile-client/        # React Native客户端
├── web-client/          # Web客户端(可选)
├── docker/              # Docker配置
├── deployment/          # 部署配置
└── docs/               # 文档
```

## 快速开始

### 环境要求
- Node.js 18+
- Python 3.8+
- React Native CLI
- Docker (可选)

### 安装依赖
```bash
# 信令服务器
cd signaling-server && npm install

# 数字人API
cd digital-human-api && pip install -r requirements.txt

# 移动客户端
cd mobile-client && npm install
```

### 运行开发环境
```bash
# 启动信令服务器
cd signaling-server && npm run dev

# 启动数字人API
cd digital-human-api && uvicorn main:app --reload

# 启动移动客户端
cd mobile-client && npx react-native run-android
```

## 部署

支持以下免费云平台：
- Heroku (免费tier)
- Railway
- Render
- Vercel (前端)

## 许可证

MIT License
