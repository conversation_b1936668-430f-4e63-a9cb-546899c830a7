import os
from typing import List, Optional
from pydantic import BaseSettings, validator
from datetime import datetime
import pytz

class Settings(BaseSettings):
    """应用配置"""
    
    # 基础配置
    APP_NAME: str = "Digital Human API"
    VERSION: str = "1.0.0"
    ENVIRONMENT: str = "development"
    DEBUG: bool = True
    
    # 服务器配置
    HOST: str = "0.0.0.0"
    PORT: int = 8000
    
    # 安全配置
    SECRET_KEY: str = "your-secret-key-change-this-in-production"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 60 * 24  # 24小时
    
    # CORS配置
    CORS_ORIGINS: List[str] = [
        "http://localhost:3000",
        "http://localhost:8081",
        "http://localhost:19006"
    ]
    
    ALLOWED_HOSTS: List[str] = ["*"]
    
    # 数据库配置
    REDIS_URL: str = "redis://localhost:6379"
    
    # 文件配置
    UPLOAD_DIR: str = "uploads"
    TEMP_DIR: str = "temp"
    MAX_FILE_SIZE: int = 50 * 1024 * 1024  # 50MB
    ALLOWED_EXTENSIONS: List[str] = [".jpg", ".jpeg", ".png", ".mp4", ".wav", ".mp3"]
    
    # AI模型配置
    MODELS_DIR: str = "models"
    DEVICE: str = "cuda"  # cuda, cpu, mps
    
    # SadTalker配置
    SADTALKER_MODEL_PATH: str = "models/sadtalker"
    SADTALKER_CHECKPOINT: str = "checkpoints/SadTalker_V002.pth"
    
    # Wav2Lip配置
    WAV2LIP_MODEL_PATH: str = "models/wav2lip"
    WAV2LIP_CHECKPOINT: str = "checkpoints/wav2lip_gan.pth"
    
    # Real-ESRGAN配置
    REAL_ESRGAN_MODEL_PATH: str = "models/real-esrgan"
    REAL_ESRGAN_MODEL: str = "RealESRGAN_x4plus"
    
    # Whisper配置
    WHISPER_MODEL_SIZE: str = "base"
    WHISPER_LANGUAGE: str = "zh"
    
    # TTS配置
    TTS_MODEL_PATH: str = "models/coqui-tts"
    TTS_MODEL_NAME: str = "tts_models/zh/baker/tacotron2-DDC-GST"
    
    # RVC配置
    RVC_MODEL_PATH: str = "models/rvc"
    
    # 日志配置
    LOG_LEVEL: str = "INFO"
    LOG_FILE: str = "logs/app.log"
    
    # 性能配置
    MAX_WORKERS: int = 4
    BATCH_SIZE: int = 1
    
    @validator("CORS_ORIGINS", pre=True)
    def assemble_cors_origins(cls, v):
        if isinstance(v, str):
            return [i.strip() for i in v.split(",")]
        return v
    
    @validator("ALLOWED_HOSTS", pre=True)
    def assemble_allowed_hosts(cls, v):
        if isinstance(v, str):
            return [i.strip() for i in v.split(",")]
        return v
    
    @validator("DEVICE")
    def validate_device(cls, v):
        import torch
        if v == "cuda" and not torch.cuda.is_available():
            return "cpu"
        return v
    
    def get_current_timestamp(self) -> str:
        """获取当前时间戳"""
        return datetime.now(pytz.UTC).isoformat()
    
    def get_model_path(self, model_name: str) -> str:
        """获取模型路径"""
        return os.path.join(self.MODELS_DIR, model_name)
    
    def get_upload_path(self, filename: str) -> str:
        """获取上传文件路径"""
        return os.path.join(self.UPLOAD_DIR, filename)
    
    def get_temp_path(self, filename: str) -> str:
        """获取临时文件路径"""
        return os.path.join(self.TEMP_DIR, filename)
    
    class Config:
        env_file = ".env"
        case_sensitive = True

# 创建全局设置实例
settings = Settings()
