from fastapi import Fast<PERSON><PERSON>, HTTPException, Depends, UploadFile, File, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import J<PERSON><PERSON>esponse, FileResponse
from fastapi.staticfiles import StaticFiles
import uvicorn
import os
from pathlib import Path
from loguru import logger
from dotenv import load_dotenv

from app.core.config import settings
from app.core.security import get_current_user
from app.api.v1 import api_router
from app.core.exceptions import setup_exception_handlers
from app.core.middleware import setup_middleware
from app.services.model_manager import ModelManager

# 加载环境变量
load_dotenv()

# 创建FastAPI应用
app = FastAPI(
    title="Digital Human API",
    description="数字人视频通话API服务",
    version="1.0.0",
    docs_url="/docs" if settings.ENVIRONMENT == "development" else None,
    redoc_url="/redoc" if settings.ENVIRONMENT == "development" else None,
)

# 设置中间件
setup_middleware(app)

# 设置异常处理
setup_exception_handlers(app)

# CORS配置
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 信任的主机
app.add_middleware(
    TrustedHostMiddleware,
    allowed_hosts=settings.ALLOWED_HOSTS
)

# 静态文件服务
app.mount("/static", StaticFiles(directory="static"), name="static")
app.mount("/uploads", StaticFiles(directory="uploads"), name="uploads")

# API路由
app.include_router(api_router, prefix="/api/v1")

# 全局模型管理器
model_manager = ModelManager()

@app.on_event("startup")
async def startup_event():
    """应用启动时的初始化"""
    logger.info("Starting Digital Human API...")
    
    # 创建必要的目录
    os.makedirs("uploads", exist_ok=True)
    os.makedirs("static", exist_ok=True)
    os.makedirs("temp", exist_ok=True)
    os.makedirs("models", exist_ok=True)
    
    # 初始化模型管理器
    await model_manager.initialize()
    
    logger.info("Digital Human API started successfully")

@app.on_event("shutdown")
async def shutdown_event():
    """应用关闭时的清理"""
    logger.info("Shutting down Digital Human API...")
    
    # 清理模型资源
    await model_manager.cleanup()
    
    logger.info("Digital Human API shutdown complete")

@app.get("/")
async def root():
    """根路径"""
    return {
        "message": "Digital Human API",
        "version": "1.0.0",
        "status": "running"
    }

@app.get("/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "timestamp": settings.get_current_timestamp(),
        "version": "1.0.0",
        "models_loaded": await model_manager.get_loaded_models()
    }

@app.get("/models/status")
async def models_status():
    """模型状态检查"""
    return await model_manager.get_models_status()

# 错误处理
@app.exception_handler(404)
async def not_found_handler(request, exc):
    return JSONResponse(
        status_code=404,
        content={"error": "Resource not found"}
    )

@app.exception_handler(500)
async def internal_error_handler(request, exc):
    logger.error(f"Internal server error: {exc}")
    return JSONResponse(
        status_code=500,
        content={"error": "Internal server error"}
    )

if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.ENVIRONMENT == "development",
        log_level=settings.LOG_LEVEL.lower()
    )
