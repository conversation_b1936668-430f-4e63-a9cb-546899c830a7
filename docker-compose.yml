version: '3.8'

services:
  # 信令服务器
  signaling-server:
    build: ./signaling-server
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=development
      - PORT=3000
    volumes:
      - ./signaling-server:/app
      - /app/node_modules
    depends_on:
      - redis

  # 数字人API服务
  digital-human-api:
    build: ./digital-human-api
    ports:
      - "8000:8000"
    environment:
      - PYTHONPATH=/app
      - ENVIRONMENT=development
    volumes:
      - ./digital-human-api:/app
      - ./models:/app/models  # 模型文件挂载
    depends_on:
      - redis
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]  # GPU支持

  # Redis缓存
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

  # TURN服务器 (Coturn)
  coturn:
    image: coturn/coturn:latest
    ports:
      - "3478:3478/udp"
      - "3478:3478/tcp"
      - "5349:5349/udp"
      - "5349:5349/tcp"
      - "49152-65535:49152-65535/udp"
    environment:
      - TURN_USERNAME=user
      - TURN_PASSWORD=password
      - TURN_REALM=localhost
    volumes:
      - ./docker/coturn/turnserver.conf:/etc/coturn/turnserver.conf

volumes:
  redis_data:

networks:
  default:
    driver: bridge
