# 开发指南

## 环境搭建

### 1. 系统要求
- **操作系统**: Windows 10/11, macOS 10.15+, Ubuntu 18.04+
- **Node.js**: 18.0+
- **Python**: 3.8+
- **Git**: 2.0+
- **Docker**: 20.0+ (可选)

### 2. GPU要求 (推荐)
- **NVIDIA GPU**: GTX 1060 6GB+ 或同等性能
- **CUDA**: 11.0+
- **显存**: 6GB+ (用于AI模型推理)

### 3. 开发工具
- **IDE**: VS Code, PyCharm, WebStorm
- **移动端调试**: Android Studio, Xcode
- **API测试**: Postman, Insomnia

## 快速开始

### 1. 克隆项目
```bash
git clone <repository-url>
cd 数字人aa
```

### 2. 安装依赖
```bash
# 安装根目录依赖
npm install

# 安装所有子项目依赖
npm run install:all
```

### 3. 环境配置
```bash
# 复制环境配置文件
cp .env.example .env

# 编辑配置文件
nano .env
```

### 4. 下载AI模型
```bash
# 创建模型目录
mkdir -p models/{sadtalker,wav2lip,real-esrgan,whisper,coqui-tts,rvc}

# 下载预训练模型 (需要手动下载)
# SadTalker: https://github.com/OpenTalker/SadTalker
# Wav2Lip: https://github.com/Rudrabha/Wav2Lip
# Real-ESRGAN: https://github.com/xinntao/Real-ESRGAN
```

### 5. 启动开发环境
```bash
# 使用Docker (推荐)
docker-compose up -d

# 或手动启动各服务
npm run dev
```

## 开发流程

### 1. 功能开发
```bash
# 创建功能分支
git checkout -b feature/new-feature

# 开发完成后提交
git add .
git commit -m "feat: add new feature"
git push origin feature/new-feature
```

### 2. 代码规范
- **JavaScript**: ESLint + Prettier
- **Python**: Black + Flake8
- **提交信息**: Conventional Commits

### 3. 测试
```bash
# 运行单元测试
npm run test

# 运行集成测试
npm run test:integration

# 运行端到端测试
npm run test:e2e
```

## 调试指南

### 1. 信令服务器调试
```bash
cd signaling-server
npm run dev:debug
```

### 2. 数字人API调试
```bash
cd digital-human-api
python -m debugpy --listen 5678 --wait-for-client -m uvicorn main:app --reload
```

### 3. 移动端调试
```bash
cd mobile-client
npx react-native run-android --variant=debug
npx react-native log-android
```

### 4. WebRTC调试
- 使用 `chrome://webrtc-internals/` 查看连接状态
- 检查STUN/TURN服务器配置
- 监控网络质量和延迟

## 常见问题

### 1. 模型加载失败
- 检查模型文件路径
- 确认GPU内存充足
- 验证CUDA版本兼容性

### 2. WebRTC连接失败
- 检查STUN/TURN服务器状态
- 确认防火墙设置
- 验证网络连通性

### 3. 移动端构建失败
- 清理缓存: `npx react-native clean`
- 重新安装依赖: `rm -rf node_modules && npm install`
- 检查Android/iOS SDK版本

## 性能优化

### 1. AI模型优化
- 使用模型量化减少内存占用
- 实现模型缓存机制
- 批处理提高吞吐量

### 2. 网络优化
- 启用gzip压缩
- 使用CDN加速静态资源
- 实现断线重连机制

### 3. 客户端优化
- 图片懒加载
- 组件按需加载
- 内存泄漏检测

## 部署准备

### 1. 生产环境配置
```bash
# 设置生产环境变量
export NODE_ENV=production
export PYTHON_ENV=production
```

### 2. 构建生产版本
```bash
npm run build
```

### 3. 安全检查
```bash
# 依赖安全扫描
npm audit
pip-audit

# 代码安全扫描
npm run security-check
```
