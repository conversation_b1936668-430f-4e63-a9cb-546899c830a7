{"name": "digital-human-video-call", "version": "1.0.0", "description": "零预算数字人视频通话应用", "main": "index.js", "scripts": {"dev": "concurrently \"npm run dev:signaling\" \"npm run dev:api\"", "dev:signaling": "cd signaling-server && npm run dev", "dev:api": "cd digital-human-api && python -m uvicorn main:app --reload", "build": "npm run build:signaling && npm run build:client", "build:signaling": "cd signaling-server && npm run build", "build:client": "cd mobile-client && npm run build", "install:all": "npm run install:signaling && npm run install:api && npm run install:client", "install:signaling": "cd signaling-server && npm install", "install:api": "cd digital-human-api && pip install -r requirements.txt", "install:client": "cd mobile-client && npm install"}, "keywords": ["digital-human", "webrtc", "video-call", "ai", "react-native"], "author": "Digital Human Team", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2"}, "workspaces": ["signaling-server", "mobile-client", "web-client"]}