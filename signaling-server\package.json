{"name": "digital-human-signaling-server", "version": "1.0.0", "description": "WebRTC信令服务器", "main": "src/index.js", "scripts": {"start": "node src/index.js", "dev": "nodemon src/index.js", "dev:debug": "nodemon --inspect src/index.js", "build": "echo 'No build step required'", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src/", "lint:fix": "eslint src/ --fix"}, "dependencies": {"express": "^4.18.2", "socket.io": "^4.7.2", "cors": "^2.8.5", "helmet": "^7.0.0", "dotenv": "^16.3.1", "redis": "^4.6.7", "jsonwebtoken": "^9.0.2", "bcryptjs": "^2.4.3", "express-rate-limit": "^6.8.1", "winston": "^3.10.0", "uuid": "^9.0.0", "joi": "^17.9.2"}, "devDependencies": {"nodemon": "^3.0.1", "jest": "^29.6.2", "supertest": "^6.3.3", "eslint": "^8.45.0", "eslint-config-standard": "^17.1.0", "eslint-plugin-import": "^2.27.5", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^6.1.1"}, "keywords": ["webrtc", "signaling", "socket.io", "video-call"], "author": "Digital Human Team", "license": "MIT", "engines": {"node": ">=18.0.0"}}