const logger = require('../utils/logger')
const { v4: uuidv4 } = require('uuid')

// 房间状态管理
const rooms = new Map()

// 用户加入房间
const joinRoom = (socket, data, io) => {
  try {
    const { roomId, roomName, isPrivate = false } = data
    const finalRoomId = roomId || uuidv4()
    
    // 离开之前的房间
    leaveCurrentRoom(socket)
    
    // 加入新房间
    socket.join(finalRoomId)
    socket.currentRoom = finalRoomId
    
    // 更新房间信息
    if (!rooms.has(finalRoomId)) {
      rooms.set(finalRoomId, {
        id: finalRoomId,
        name: roomName || `Room ${finalRoomId.substring(0, 8)}`,
        users: new Map(),
        createdAt: new Date(),
        isPrivate,
        createdBy: socket.userId
      })
    }
    
    const room = rooms.get(finalRoomId)
    room.users.set(socket.userId, {
      userId: socket.userId,
      username: socket.username,
      socketId: socket.id,
      joinedAt: new Date()
    })
    
    logger.info('User joined room:', { 
      userId: socket.userId, 
      roomId: finalRoomId,
      userCount: room.users.size
    })
    
    // 通知用户加入成功
    socket.emit('room-joined', {
      roomId: finalRoomId,
      roomName: room.name,
      users: Array.from(room.users.values())
    })
    
    // 通知房间内其他用户
    socket.to(finalRoomId).emit('user-joined', {
      user: {
        userId: socket.userId,
        username: socket.username
      },
      roomId: finalRoomId,
      userCount: room.users.size
    })
    
    // 发送房间用户列表
    io.to(finalRoomId).emit('room-users', {
      roomId: finalRoomId,
      users: Array.from(room.users.values())
    })
    
  } catch (error) {
    logger.error('Error joining room:', error)
    socket.emit('error', { message: 'Failed to join room' })
  }
}

// 用户离开房间
const leaveRoom = (socket, data, io) => {
  try {
    const roomId = data?.roomId || socket.currentRoom
    if (!roomId) return
    
    leaveRoomById(socket, roomId, io)
    
  } catch (error) {
    logger.error('Error leaving room:', error)
  }
}

// 根据房间ID离开房间
const leaveRoomById = (socket, roomId, io) => {
  if (!rooms.has(roomId)) return
  
  const room = rooms.get(roomId)
  room.users.delete(socket.userId)
  
  socket.leave(roomId)
  if (socket.currentRoom === roomId) {
    socket.currentRoom = null
  }
  
  logger.info('User left room:', { 
    userId: socket.userId, 
    roomId,
    userCount: room.users.size
  })
  
  // 通知房间内其他用户
  socket.to(roomId).emit('user-left', {
    userId: socket.userId,
    username: socket.username,
    roomId,
    userCount: room.users.size
  })
  
  // 更新房间用户列表
  io.to(roomId).emit('room-users', {
    roomId,
    users: Array.from(room.users.values())
  })
  
  // 如果房间为空，删除房间
  if (room.users.size === 0) {
    rooms.delete(roomId)
    logger.info('Room deleted (empty):', { roomId })
  }
}

// 离开当前房间
const leaveCurrentRoom = (socket) => {
  if (socket.currentRoom) {
    const roomId = socket.currentRoom
    if (rooms.has(roomId)) {
      const room = rooms.get(roomId)
      room.users.delete(socket.userId)
      socket.leave(roomId)
      
      // 如果房间为空，删除房间
      if (room.users.size === 0) {
        rooms.delete(roomId)
      }
    }
    socket.currentRoom = null
  }
}

// 处理用户断开连接
const handleDisconnect = (socket, io) => {
  try {
    if (socket.currentRoom) {
      leaveRoomById(socket, socket.currentRoom, io)
    }
    
    logger.info('User disconnected and cleaned up:', { 
      userId: socket.userId 
    })
    
  } catch (error) {
    logger.error('Error handling disconnect:', error)
  }
}

// 获取房间列表
const getRoomList = () => {
  const roomList = []
  for (const [roomId, room] of rooms) {
    if (!room.isPrivate) {
      roomList.push({
        id: room.id,
        name: room.name,
        userCount: room.users.size,
        createdAt: room.createdAt,
        createdBy: room.createdBy
      })
    }
  }
  return roomList
}

// 获取房间详情
const getRoomDetails = (roomId) => {
  if (!rooms.has(roomId)) {
    return null
  }
  
  const room = rooms.get(roomId)
  return {
    id: room.id,
    name: room.name,
    users: Array.from(room.users.values()),
    userCount: room.users.size,
    createdAt: room.createdAt,
    createdBy: room.createdBy,
    isPrivate: room.isPrivate
  }
}

// 创建私人房间
const createPrivateRoom = (socket, data) => {
  try {
    const roomId = uuidv4()
    const { roomName, inviteUsers = [] } = data
    
    rooms.set(roomId, {
      id: roomId,
      name: roomName || `Private Room ${roomId.substring(0, 8)}`,
      users: new Map(),
      createdAt: new Date(),
      isPrivate: true,
      createdBy: socket.userId,
      inviteUsers
    })
    
    logger.info('Private room created:', { 
      roomId, 
      createdBy: socket.userId,
      inviteUsers 
    })
    
    socket.emit('private-room-created', {
      roomId,
      roomName: rooms.get(roomId).name,
      inviteCode: roomId
    })
    
    return roomId
    
  } catch (error) {
    logger.error('Error creating private room:', error)
    socket.emit('error', { message: 'Failed to create private room' })
    return null
  }
}

module.exports = {
  joinRoom,
  leaveRoom,
  handleDisconnect,
  getRoomList,
  getRoomDetails,
  createPrivateRoom,
  rooms
}
