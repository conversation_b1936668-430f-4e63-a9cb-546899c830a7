const logger = require('../utils/logger')
const { validateSignalingData } = require('../utils/validation')

// 处理WebRTC Offer
const handleOffer = (socket, data, io) => {
  try {
    const validation = validateSignalingData(data, ['targetUserId', 'offer'])
    if (!validation.isValid) {
      socket.emit('error', { message: validation.error })
      return
    }

    const { targetUserId, offer, roomId } = data
    
    logger.info('Handling offer:', { 
      from: socket.userId, 
      to: targetUserId, 
      roomId 
    })

    // 查找目标用户的socket
    const targetSocket = findUserSocket(io, targetUserId)
    if (!targetSocket) {
      socket.emit('error', { message: 'Target user not found or offline' })
      return
    }

    // 转发offer给目标用户
    targetSocket.emit('offer', {
      fromUserId: socket.userId,
      fromUsername: socket.username,
      offer,
      roomId
    })

    logger.info('Offer forwarded successfully')
  } catch (error) {
    logger.error('Error handling offer:', error)
    socket.emit('error', { message: 'Failed to process offer' })
  }
}

// 处理WebRTC Answer
const handleAnswer = (socket, data, io) => {
  try {
    const validation = validateSignalingData(data, ['targetUserId', 'answer'])
    if (!validation.isValid) {
      socket.emit('error', { message: validation.error })
      return
    }

    const { targetUserId, answer, roomId } = data
    
    logger.info('Handling answer:', { 
      from: socket.userId, 
      to: targetUserId, 
      roomId 
    })

    // 查找目标用户的socket
    const targetSocket = findUserSocket(io, targetUserId)
    if (!targetSocket) {
      socket.emit('error', { message: 'Target user not found or offline' })
      return
    }

    // 转发answer给目标用户
    targetSocket.emit('answer', {
      fromUserId: socket.userId,
      fromUsername: socket.username,
      answer,
      roomId
    })

    logger.info('Answer forwarded successfully')
  } catch (error) {
    logger.error('Error handling answer:', error)
    socket.emit('error', { message: 'Failed to process answer' })
  }
}

// 处理ICE候选
const handleIceCandidate = (socket, data, io) => {
  try {
    const validation = validateSignalingData(data, ['targetUserId', 'candidate'])
    if (!validation.isValid) {
      socket.emit('error', { message: validation.error })
      return
    }

    const { targetUserId, candidate, roomId } = data
    
    logger.debug('Handling ICE candidate:', { 
      from: socket.userId, 
      to: targetUserId 
    })

    // 查找目标用户的socket
    const targetSocket = findUserSocket(io, targetUserId)
    if (!targetSocket) {
      // ICE候选失败不需要报错，这是正常的网络协商过程
      return
    }

    // 转发ICE候选给目标用户
    targetSocket.emit('ice-candidate', {
      fromUserId: socket.userId,
      candidate,
      roomId
    })
  } catch (error) {
    logger.error('Error handling ICE candidate:', error)
  }
}

// 处理数字人控制指令
const handleDigitalHumanControl = (socket, data, io) => {
  try {
    const validation = validateSignalingData(data, ['action'])
    if (!validation.isValid) {
      socket.emit('error', { message: validation.error })
      return
    }

    const { action, targetUserId, roomId, payload } = data
    
    logger.info('Handling digital human control:', { 
      from: socket.userId, 
      action, 
      targetUserId,
      roomId 
    })

    // 如果指定了目标用户，转发给特定用户
    if (targetUserId) {
      const targetSocket = findUserSocket(io, targetUserId)
      if (targetSocket) {
        targetSocket.emit('digital-human-control', {
          fromUserId: socket.userId,
          action,
          payload,
          roomId
        })
      }
    } else if (roomId) {
      // 否则广播给房间内所有用户
      socket.to(roomId).emit('digital-human-control', {
        fromUserId: socket.userId,
        action,
        payload,
        roomId
      })
    }

    logger.info('Digital human control forwarded successfully')
  } catch (error) {
    logger.error('Error handling digital human control:', error)
    socket.emit('error', { message: 'Failed to process digital human control' })
  }
}

// 查找用户的socket连接
const findUserSocket = (io, userId) => {
  for (const [socketId, socket] of io.sockets.sockets) {
    if (socket.userId === userId) {
      return socket
    }
  }
  return null
}

// 获取在线用户列表
const getOnlineUsers = (io) => {
  const users = []
  for (const [socketId, socket] of io.sockets.sockets) {
    if (socket.userId) {
      users.push({
        userId: socket.userId,
        username: socket.username,
        socketId
      })
    }
  }
  return users
}

module.exports = {
  handleOffer,
  handleAnswer,
  handleIceCandidate,
  handleDigitalHumanControl,
  findUserSocket,
  getOnlineUsers
}
