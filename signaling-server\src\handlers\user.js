const express = require('express')
const bcrypt = require('bcryptjs')
const { v4: uuidv4 } = require('uuid')
const { generateToken, authenticateHTTP } = require('../middleware/auth')
const { validateUserData } = require('../utils/validation')
const logger = require('../utils/logger')

const router = express.Router()

// 模拟用户数据库 (生产环境应使用真实数据库)
const users = new Map()

// 用户注册
router.post('/register', async (req, res) => {
  try {
    const validation = validateUserData(req.body, ['username', 'email', 'password'])
    if (!validation.isValid) {
      return res.status(400).json({ error: validation.error })
    }

    const { username, email, password } = req.body

    // 检查用户是否已存在
    const existingUser = Array.from(users.values()).find(
      user => user.email === email || user.username === username
    )
    
    if (existingUser) {
      return res.status(409).json({ error: 'User already exists' })
    }

    // 密码加密
    const saltRounds = 10
    const hashedPassword = await bcrypt.hash(password, saltRounds)

    // 创建新用户
    const userId = uuidv4()
    const user = {
      id: userId,
      username,
      email,
      password: hashedPassword,
      createdAt: new Date(),
      isOnline: false,
      avatar: null
    }

    users.set(userId, user)

    // 生成JWT token
    const token = generateToken(user)

    logger.info('User registered:', { userId, username, email })

    res.status(201).json({
      message: 'User registered successfully',
      user: {
        id: user.id,
        username: user.username,
        email: user.email,
        createdAt: user.createdAt
      },
      token
    })

  } catch (error) {
    logger.error('Registration error:', error)
    res.status(500).json({ error: 'Registration failed' })
  }
})

// 用户登录
router.post('/login', async (req, res) => {
  try {
    const validation = validateUserData(req.body, ['email', 'password'])
    if (!validation.isValid) {
      return res.status(400).json({ error: validation.error })
    }

    const { email, password } = req.body

    // 查找用户
    const user = Array.from(users.values()).find(user => user.email === email)
    if (!user) {
      return res.status(401).json({ error: 'Invalid credentials' })
    }

    // 验证密码
    const isValidPassword = await bcrypt.compare(password, user.password)
    if (!isValidPassword) {
      return res.status(401).json({ error: 'Invalid credentials' })
    }

    // 更新在线状态
    user.isOnline = true
    user.lastLoginAt = new Date()

    // 生成JWT token
    const token = generateToken(user)

    logger.info('User logged in:', { userId: user.id, username: user.username })

    res.json({
      message: 'Login successful',
      user: {
        id: user.id,
        username: user.username,
        email: user.email,
        avatar: user.avatar,
        lastLoginAt: user.lastLoginAt
      },
      token
    })

  } catch (error) {
    logger.error('Login error:', error)
    res.status(500).json({ error: 'Login failed' })
  }
})

// 获取用户信息
router.get('/profile', authenticateHTTP, (req, res) => {
  try {
    const user = users.get(req.user.userId)
    if (!user) {
      return res.status(404).json({ error: 'User not found' })
    }

    res.json({
      user: {
        id: user.id,
        username: user.username,
        email: user.email,
        avatar: user.avatar,
        createdAt: user.createdAt,
        lastLoginAt: user.lastLoginAt,
        isOnline: user.isOnline
      }
    })

  } catch (error) {
    logger.error('Profile fetch error:', error)
    res.status(500).json({ error: 'Failed to fetch profile' })
  }
})

module.exports = router
