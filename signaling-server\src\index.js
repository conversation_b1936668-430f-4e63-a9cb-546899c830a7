const express = require('express')
const { createServer } = require('http')
const { Server } = require('socket.io')
const cors = require('cors')
const helmet = require('helmet')
const rateLimit = require('express-rate-limit')
require('dotenv').config()

const logger = require('./utils/logger')
const { authenticateToken } = require('./middleware/auth')
const signalingHandler = require('./handlers/signaling')
const roomHandler = require('./handlers/room')
const userHandler = require('./handlers/user')

const app = express()
const server = createServer(app)

// 中间件配置
app.use(helmet())
app.use(cors({
  origin: process.env.CORS_ORIGINS?.split(',') || ['http://localhost:3000', 'http://localhost:8081'],
  credentials: true
}))

// 限流配置
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 100, // 限制每个IP 15分钟内最多100个请求
  message: 'Too many requests from this IP'
})
app.use('/api/', limiter)

app.use(express.json({ limit: '10mb' }))
app.use(express.urlencoded({ extended: true }))

// Socket.IO配置
const io = new Server(server, {
  cors: {
    origin: process.env.CORS_ORIGINS?.split(',') || ['http://localhost:3000', 'http://localhost:8081'],
    methods: ['GET', 'POST']
  },
  transports: ['websocket', 'polling']
})

// 健康检查
app.get('/health', (req, res) => {
  res.json({ 
    status: 'ok', 
    timestamp: new Date().toISOString(),
    uptime: process.uptime()
  })
})

// API路由
app.use('/api/users', userHandler)

// Socket.IO连接处理
io.use(authenticateToken)

io.on('connection', (socket) => {
  logger.info(`User connected: ${socket.userId}`)
  
  // 用户加入房间
  socket.on('join-room', (data) => {
    roomHandler.joinRoom(socket, data, io)
  })
  
  // 用户离开房间
  socket.on('leave-room', (data) => {
    roomHandler.leaveRoom(socket, data, io)
  })
  
  // WebRTC信令处理
  socket.on('offer', (data) => {
    signalingHandler.handleOffer(socket, data, io)
  })
  
  socket.on('answer', (data) => {
    signalingHandler.handleAnswer(socket, data, io)
  })
  
  socket.on('ice-candidate', (data) => {
    signalingHandler.handleIceCandidate(socket, data, io)
  })
  
  // 数字人控制
  socket.on('digital-human-control', (data) => {
    signalingHandler.handleDigitalHumanControl(socket, data, io)
  })
  
  // 断开连接处理
  socket.on('disconnect', () => {
    logger.info(`User disconnected: ${socket.userId}`)
    roomHandler.handleDisconnect(socket, io)
  })
  
  // 错误处理
  socket.on('error', (error) => {
    logger.error(`Socket error for user ${socket.userId}:`, error)
  })
})

// 全局错误处理
app.use((err, req, res, next) => {
  logger.error('Unhandled error:', err)
  res.status(500).json({ error: 'Internal server error' })
})

// 启动服务器
const PORT = process.env.PORT || 3000
server.listen(PORT, () => {
  logger.info(`Signaling server running on port ${PORT}`)
  logger.info(`Environment: ${process.env.NODE_ENV || 'development'}`)
})

// 优雅关闭
process.on('SIGTERM', () => {
  logger.info('SIGTERM received, shutting down gracefully')
  server.close(() => {
    logger.info('Process terminated')
    process.exit(0)
  })
})

module.exports = { app, server, io }
