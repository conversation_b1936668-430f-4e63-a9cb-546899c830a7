const jwt = require('jsonwebtoken')
const logger = require('../utils/logger')

const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key'

// HTTP请求认证中间件
const authenticateHTTP = (req, res, next) => {
  const authHeader = req.headers.authorization
  const token = authHeader && authHeader.split(' ')[1]

  if (!token) {
    return res.status(401).json({ error: 'Access token required' })
  }

  jwt.verify(token, JWT_SECRET, (err, user) => {
    if (err) {
      logger.warn('Invalid token attempt:', { token: token.substring(0, 20) + '...' })
      return res.status(403).json({ error: 'Invalid token' })
    }
    req.user = user
    next()
  })
}

// Socket.IO认证中间件
const authenticateToken = (socket, next) => {
  const token = socket.handshake.auth.token || socket.handshake.query.token

  if (!token) {
    logger.warn('Socket connection without token')
    return next(new Error('Authentication error: No token provided'))
  }

  jwt.verify(token, JWT_SECRET, (err, decoded) => {
    if (err) {
      logger.warn('Socket authentication failed:', { error: err.message })
      return next(new Error('Authentication error: Invalid token'))
    }

    socket.userId = decoded.userId
    socket.username = decoded.username
    socket.userInfo = decoded
    
    logger.info('Socket authenticated:', { 
      userId: decoded.userId, 
      username: decoded.username 
    })
    
    next()
  })
}

// 生成JWT Token
const generateToken = (user) => {
  const payload = {
    userId: user.id,
    username: user.username,
    email: user.email
  }
  
  return jwt.sign(payload, JWT_SECRET, { 
    expiresIn: process.env.JWT_EXPIRES_IN || '24h' 
  })
}

// 验证Token
const verifyToken = (token) => {
  try {
    return jwt.verify(token, JWT_SECRET)
  } catch (error) {
    logger.error('Token verification failed:', error)
    return null
  }
}

module.exports = {
  authenticateHTTP,
  authenticateToken,
  generateToken,
  verifyToken
}
