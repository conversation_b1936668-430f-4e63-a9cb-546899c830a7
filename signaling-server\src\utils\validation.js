const Joi = require('joi')

// 用户数据验证
const validateUserData = (data, requiredFields = []) => {
  const schema = Joi.object({
    username: Joi.string().alphanum().min(3).max(30),
    email: Joi.string().email(),
    password: Joi.string().min(6).max(128),
    avatar: Joi.string().uri().allow(null, '')
  })

  // 检查必需字段
  for (const field of requiredFields) {
    if (!data[field]) {
      return {
        isValid: false,
        error: `${field} is required`
      }
    }
  }

  const { error } = schema.validate(data)
  if (error) {
    return {
      isValid: false,
      error: error.details[0].message
    }
  }

  return { isValid: true }
}

// 信令数据验证
const validateSignalingData = (data, requiredFields = []) => {
  const schema = Joi.object({
    targetUserId: Joi.string().uuid(),
    roomId: Joi.string().uuid().allow(null, ''),
    offer: Joi.object(),
    answer: Joi.object(),
    candidate: Joi.object(),
    action: Joi.string().valid(
      'start-digital-human',
      'stop-digital-human',
      'change-avatar',
      'change-voice',
      'send-message'
    ),
    payload: Joi.object()
  })

  // 检查必需字段
  for (const field of requiredFields) {
    if (data[field] === undefined || data[field] === null) {
      return {
        isValid: false,
        error: `${field} is required`
      }
    }
  }

  const { error } = schema.validate(data)
  if (error) {
    return {
      isValid: false,
      error: error.details[0].message
    }
  }

  return { isValid: true }
}

// 房间数据验证
const validateRoomData = (data, requiredFields = []) => {
  const schema = Joi.object({
    roomId: Joi.string().uuid().allow(null, ''),
    roomName: Joi.string().min(1).max(100),
    isPrivate: Joi.boolean(),
    inviteUsers: Joi.array().items(Joi.string().uuid())
  })

  // 检查必需字段
  for (const field of requiredFields) {
    if (!data[field]) {
      return {
        isValid: false,
        error: `${field} is required`
      }
    }
  }

  const { error } = schema.validate(data)
  if (error) {
    return {
      isValid: false,
      error: error.details[0].message
    }
  }

  return { isValid: true }
}

// 通用数据清理
const sanitizeData = (data) => {
  if (typeof data === 'string') {
    return data.trim()
  }
  
  if (typeof data === 'object' && data !== null) {
    const sanitized = {}
    for (const [key, value] of Object.entries(data)) {
      sanitized[key] = sanitizeData(value)
    }
    return sanitized
  }
  
  return data
}

module.exports = {
  validateUserData,
  validateSignalingData,
  validateRoomData,
  sanitizeData
}
