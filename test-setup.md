# 测试环境搭建指南

## 前置要求

### 1. 安装Node.js
```bash
# 下载并安装Node.js 18+
# 访问: https://nodejs.org/
# 或使用包管理器:
winget install OpenJS.NodeJS
```

### 2. 安装Python
```bash
# 下载并安装Python 3.8+
# 访问: https://python.org/
# 或使用包管理器:
winget install Python.Python.3.11
```

### 3. 验证安装
```bash
node --version  # 应该显示 v18.x.x 或更高
npm --version   # 应该显示 9.x.x 或更高
python --version # 应该显示 Python 3.8+ 
pip --version   # 应该显示 pip 版本
```

## 快速测试步骤

### 1. 安装依赖
```bash
# 在项目根目录
npm install

# 信令服务器
cd signaling-server
npm install
cd ..

# 数字人API
cd digital-human-api
pip install -r requirements.txt
cd ..
```

### 2. 配置环境变量
```bash
# 复制环境配置
cp .env.example .env
cp signaling-server/.env.example signaling-server/.env
```

### 3. 启动服务测试

#### 启动信令服务器
```bash
cd signaling-server
npm run dev
```

#### 启动数字人API（新终端）
```bash
cd digital-human-api
python -m uvicorn main:app --reload --port 8000
```

### 4. 测试API端点

#### 信令服务器测试
```bash
# 健康检查
curl http://localhost:3000/health

# 用户注册
curl -X POST http://localhost:3000/api/users/register \
  -H "Content-Type: application/json" \
  -d '{"username":"testuser","email":"<EMAIL>","password":"password123"}'
```

#### 数字人API测试
```bash
# 健康检查
curl http://localhost:8000/health

# 模型状态
curl http://localhost:8000/models/status
```

## 常见问题解决

### 1. Node.js未安装
- 下载安装: https://nodejs.org/
- 重启终端后再试

### 2. Python未安装
- 下载安装: https://python.org/
- 确保勾选"Add to PATH"

### 3. 端口被占用
```bash
# 查看端口占用
netstat -ano | findstr :3000
netstat -ano | findstr :8000

# 修改端口（在.env文件中）
PORT=3001
```

### 4. 权限问题
```bash
# 以管理员身份运行PowerShell
# 或使用 --force 参数
npm install --force
```
